plugins {
    id 'java-library'
}

repositories {
    mavenCentral()
}

dependencies {

    implementation project(':api')
    implementation project(':services')
    implementation project(':components:useraudit')
    implementation project(':components:issuemanager')
    implementation project(':components:localisation')

    api "io.swagger.core.v3:swagger-annotations:${swaggerCoreVersion}"

    implementation 'org.springframework:spring-webmvc'
    implementation 'org.springframework.security:spring-security-web'
    implementation 'org.springframework.security:spring-security-config'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
}
