plugins {
    id 'java'
    id 'org.springframework.boot' version "${springBootVersion}"
    id 'com.google.cloud.tools.jib'
    id "com.gorylenko.gradle-git-properties" version "${gitPropertyPluginVersion}"
    id 'org.jetbrains.kotlin.jvm' version "${kotlinVersion}"
    id 'org.jetbrains.kotlin.plugin.spring' version "${kotlinVersion}"
}

apply plugin: 'io.spring.dependency-management'


springBoot {
    buildInfo()
}

configurations {
    compile.exclude module: "spring-boot-starter-tomcat"
    jaxb
}

// Configure source sets to allow Kotlin files in Java directories
sourceSets {
    main {
        kotlin {
            srcDirs = ['src/main/kotlin', 'src/main/java']
        }
        java {
            srcDirs = ['src/main/java']
        }
    }
    test {
        kotlin {
            srcDirs = ['src/test/kotlin', 'src/test/java']
        }
        java {
            srcDirs = ['src/test/java']
        }
    }
}

bootRun {
    jvmArgs += ["--add-opens=java.base/java.time=ALL-UNNAMED","--add-opens=java.base/sun.net=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.math=ALL-UNNAMED", "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED", "--add-opens=java.base/java.net=ALL-UNNAMED", "--add-opens=java.base/java.text=ALL-UNNAMED", "--add-opens=java.sql/java.sql=ALL-UNNAMED"]
}

configurations {
    custom
    runtime.extendsFrom custom
    compile.extendsFrom custom
}



test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
    systemProperty 'flyway.enabled', 'false'
}

dependencies {
    // Kotlin dependencies
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlinVersion}"
    implementation "org.jetbrains.kotlin:kotlin-reflect:${kotlinVersion}"
    testImplementation "org.jetbrains.kotlin:kotlin-test:${kotlinVersion}"
    testImplementation "org.jetbrains.kotlin:kotlin-test-junit5:${kotlinVersion}"

    runtimeOnly "org.springframework.boot:spring-boot-properties-migrator"

    implementation 'org.springframework.boot:spring-boot-starter-log4j2'

    // subs
    implementation project(':persistence')
    implementation project(':api')
    implementation project(':configuration')
    implementation project(':services')
    implementation project(':backend-shared')
    implementation project(':components:authjwt-firebase')
    implementation project(':components:rewards:rewards-tickets')
    implementation project(':components:videoads')
    implementation project(':components:geoip-db')

    implementation project(':payments:payment-adyen')
    implementation project(':payments:payment-mobile')

    implementation project(':features:social-common')

    implementation project(':features:ranking')
    implementation project(':features:rtp')
    implementation project(':features:leaderboards')
    implementation project(':features:doubleup')
    implementation project(':features:loyalty')
    implementation project(':features:missions')
    implementation project(':features:wheelspins')
    implementation project(':features:bonuscodes')

    implementation project(':features:collectibles')

    implementation project(':features:cpopups')
    implementation project(':features:jackpots')
    implementation project(':features:safes')

    implementation project(':features:milestones')
    implementation project(':features:marketing-emails')


//    dependencies {
//        compileOnly("org.springframework.boot:spring-boot-devtools")
//    }


//    implementation "io.hypersistence:hypersistence-optimizer:2.5.1"

    testImplementation project(path: ':test-util')


    // Swagger stuff
    if (project.hasProperty("swagger")) {
        implementation project(':devtools:swagger-ui')
    }
    // monitoring
    implementation "org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}"
    implementation "io.micrometer:micrometer-registry-prometheus"

    // using jetty!
    implementation "org.springframework.boot:spring-boot-starter-web:${springBootVersion}", {
        exclude group: "org.springframework.boot", module: "spring-boot-starter-tomcat"
    }
    implementation "org.springframework.boot:spring-boot-starter-jetty:${springBootVersion}", {
        exclude group: "org.eclipse.jetty.ee10.websocket"

    }
    implementation "org.springframework.boot:spring-boot-starter-security:${springBootVersion}"
    implementation "org.springframework.boot:spring-boot-starter-validation:${springBootVersion}"
    //
    implementation("com.github.gavlyukovskiy:datasource-proxy-spring-boot-starter:${datasourceProxyVersion}")

    // needs to get migrated, see: https://commons.apache.org/proper/commons-fileupload/migration.html
    implementation 'commons-fileupload:commons-fileupload:1.5'

    testImplementation project(':features:ranking')

    testImplementation "org.springframework.amqp:spring-rabbit"
   // testImplementation "org.springframework.pulsar:spring-pulsar"
    testImplementation "commons-io:commons-io:2.11.0"
}

compileJava.dependsOn.add('generateGitProperties')
compileJava.dependsOn.add('bootBuildInfo')
compileJava.inputs.files(processResources)

ext {
    // ...
    ext.getGitHash = { ->
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short=8', 'HEAD'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }
}

jib {
    from {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-base:ac21.0.1.al2net'
        platforms {
            platform {
                architecture = 'amd64'
                os = 'linux'
            }
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    to {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-backend'
        if (System.env['AWS_ECR_PW'] != null) {
            tags = ['latest', "${getGitHash()}"]
        } else {
            tags = ['latest', "LOCAL-${getGitHash()}"]
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    container {
        appRoot = '/backend'
        jvmFlags = ["@/backend/resources/java-21-args", "-Xms1024M", "-Xmx1585M", "-XX:+UseZGC", "-XX:+ZGenerational", "-XX:+UseCompressedOops", "-XX:+UseStringDeduplication"]
        ports = ['8080']
        workingDirectory = '/backend'
    }

    allowInsecureRegistries = false
}

if (System.env['AWS_ECR_PW'] == null) {
    tasks.jib.dependsOn rootProject.loginToECR
}