package com.ously.gamble;


import com.ously.gamble.util.GitInfo;
import jakarta.annotation.PostConstruct;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.core.SpringVersion;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.validation.annotation.Validated;

import java.util.Locale;
import java.util.TimeZone;

@SpringBootApplication(scanBasePackages = "com.ously.gamble")
@EnableRetry
@EnableJpaAuditing
@EntityScan(basePackages = "com.ously.gamble")
@EnableJpaRepositories(basePackages = "com.ously.gamble")
@EnableScheduling
@Validated
public class BackendApplication {

    @PostConstruct
    static void init() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }

    public static void main(String... args) {
        System.out.print("LOCALE: " + Locale.getDefault() + " | ");
        System.out.println("GIT: " + GitInfo.getGitInfoString());
        System.out.println("Spring Version : '" + SpringVersion.getVersion() + "', SpringBoot Version: '" + SpringBootVersion.getVersion() + '\'');

        var app = new SpringApplication(BackendApplication.class);
        if ("on".equals(System.getProperty("startuplogging", ""))) {
            System.out.println("Startup-Logging active. Do not use in production!");
            app.setApplicationStartup(new BufferingApplicationStartup(10000));
        }
        app.run(args);
    }

//    @Bean(name = "multipartResolver")
//    public CommonsMultipartResolver multipartResolver() {
//        var multipartResolver = new CommonsMultipartResolver();
//        multipartResolver.setMaxUploadSize(-1L);
//        return multipartResolver;
//    }

}
