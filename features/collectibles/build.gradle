buildscript {

    dependencies {
        if (project.hasProperty("swagger")) {
            println "** NO bytecode enhancements"
        } else {
            println "** Using bytecode enhancements (dep)"
            classpath "org.hibernate.orm:hibernate-gradle-plugin:$hibernateVersion"
        }
    }
}


plugins {
    id 'java-library'
}

group = 'com.ously.gamble.collectibles'


repositories {
    mavenCentral()
}


if (project.hasProperty("swagger")) {
    println "** NOT Using bytecode enhancements"

} else {
    println "** Using bytecode enhancements"
    apply plugin: 'org.hibernate.orm'
}




dependencies {

    implementation project(':api')
    implementation project(':persistence')
    implementation project(':configuration')

    implementation "org.eclipse.collections:eclipse-collections:8.2.0"
    implementation 'io.hypersistence:hypersistence-utils-hibernate-62:3.4.3'

    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation "org.springframework.amqp:spring-amqp"
    implementation "org.springframework.amqp:spring-rabbit"
    implementation 'org.springframework:spring-webmvc'
    implementation 'org.springframework.boot:spring-boot-starter-validation'


    testImplementation platform('org.junit:junit-bom:5.9.1')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

test {
    useJUnitPlatform()
}