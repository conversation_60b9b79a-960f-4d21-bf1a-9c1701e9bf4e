package com.ously.gamble.collectibles.validation;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UniqueCollectionNameValidator.class)
public @interface UniqueCollectionName {
    String message() default "Collection with this name already exists";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    String nameField() default "name";
    String excludeIdField() default "id";
}

@Component
class UniqueCollectionNameValidator implements ConstraintValidator<UniqueCollectionName, Object> {

    private String nameField;
    private String excludeIdField;

    @Autowired
    private CardCollectionRepository cardCollectionRepository;

    @Override
    public void initialize(UniqueCollectionName constraintAnnotation) {
        this.nameField = constraintAnnotation.nameField();
        this.excludeIdField = constraintAnnotation.excludeIdField();
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        if (obj == null) {
            return true;
        }

        try {
            String name = (String) BeanUtils.getPropertyDescriptor(obj.getClass(), nameField)
                    .getReadMethod().invoke(obj);

            if (name == null || name.trim().isEmpty()) {
                return true;
            }

            // excluding id of the same entity that is being updated
            Integer excludeId = null;
            var excludeIdDescriptor = BeanUtils.getPropertyDescriptor(obj.getClass(), excludeIdField);
            if (excludeIdDescriptor != null) {
                Object idValue = excludeIdDescriptor.getReadMethod().invoke(obj);
                if (idValue instanceof Integer) {
                    excludeId = (Integer) idValue;
                }
            }

            // For create requests, excludeId will be null, which is handled by the query
            return cardCollectionRepository.findByNameIgnoreCaseExcludingId(name, excludeId).isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
}
